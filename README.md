# Local Model Context Protocol Server

This is a simple implementation of a Model Context Protocol (MCP) server that provides resources, tools, and prompts to LLM applications.

## What is MCP?

The [Model Context Protocol (MCP)](https://modelcontextprotocol.io) is an open protocol that enables seamless integration between LLM applications and external data sources and tools. It provides a standardized way to connect LLMs with the context they need.

## Features

This server implements:

- **Resources**: Contextual data that can be loaded into an LLM's context
- **Tools**: Functions that can be executed by the LLM
- **Prompts**: Templated interactions for the LLM

## Available Resources

- `system://info` - Get basic system information
- `time://current` - Get the current time
- `file://{path}` - Get the content of a file
- `directory://{path}` - List the contents of a directory

## Available Tools

- `calculate(expression)` - Calculate the result of a mathematical expression
- `fetch_url(url)` - Fetch the content of a URL
- `write_file(path, content)` - Write content to a file
- `generate_random(min_value, max_value)` - Generate a random number
- `long_operation(steps)` - Simulate a long operation with progress reporting

## Available Prompts

- `simple_prompt(message)` - A simple prompt that returns the message
- `analyze_code(code)` - Prompt for analyzing code
- `conversation_starter(topic)` - Start a conversation about a topic

## Installation

1. Install the MCP Python SDK:

```bash
pip install "mcp[cli]"
```

2. Clone this repository:

```bash
git clone https://github.com/yourusername/local-mcp-server.git
cd local-mcp-server
```

## Usage

### Running the Server

You can run the server directly:

```bash
python mcp_server.py
```

### Development Mode

For testing and debugging, use the MCP Inspector:

```bash
mcp dev mcp_server.py
```

### Claude Desktop Integration

To install the server in Claude Desktop:

```bash
mcp install mcp_server.py
```

## Extending the Server

You can extend this server by adding your own resources, tools, and prompts. Simply modify the `mcp_server.py` file and add your own functions.

### Adding a Resource

```python
@mcp.resource("my-resource://{param}")
def my_resource(param: str) -> str:
    """My custom resource"""
    return f"Resource with parameter: {param}"
```

### Adding a Tool

```python
@mcp.tool()
def my_tool(param: str) -> str:
    """My custom tool"""
    return f"Tool executed with parameter: {param}"
```

### Adding a Prompt

```python
@mcp.prompt()
def my_prompt(param: str) -> str:
    """My custom prompt"""
    return f"This is a prompt with parameter: {param}"
```

## License

MIT

#!/bin/bash

# Bright Soft Website Deployment Script
# Usage: ./deploy.sh [local|production]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    mkdir -p letsencrypt
    chmod 600 letsencrypt
    print_success "Directories created"
}

# Deploy for local development
deploy_local() {
    print_status "Deploying for local development..."
    
    # Add local hosts entries reminder
    print_warning "Make sure to add these entries to your /etc/hosts file:"
    echo "127.0.0.1 brightsoft.local"
    echo "127.0.0.1 www.brightsoft.local"
    echo "127.0.0.1 traefik.brightsoft.local"
    echo ""
    
    # Start services
    print_status "Starting Docker services..."
    docker-compose up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to start..."
    sleep 10
    
    # Check if services are running
    if docker-compose ps | grep -q "Up"; then
        print_success "Services are running!"
        echo ""
        echo "🌐 Website: http://brightsoft.local"
        echo "📊 Traefik Dashboard: http://localhost:8080"
        echo ""
        echo "To view logs: docker-compose logs -f"
        echo "To stop: docker-compose down"
    else
        print_error "Some services failed to start. Check logs with: docker-compose logs"
        exit 1
    fi
}

# Deploy for production
deploy_production() {
    print_status "Deploying for production..."
    
    # Check if .env file exists
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from template..."
        cp .env.example .env
        print_warning "Please edit .env file with your domain and email settings"
        print_warning "Then run the script again"
        exit 1
    fi
    
    # Load environment variables
    source .env
    
    # Validate required variables
    if [ -z "$DOMAIN" ] || [ -z "$ACME_EMAIL" ]; then
        print_error "Please set DOMAIN and ACME_EMAIL in .env file"
        exit 1
    fi
    
    print_status "Deploying with domain: $DOMAIN"
    print_status "SSL certificates will be requested for: $ACME_EMAIL"
    
    # Start production services
    print_status "Starting production Docker services..."
    docker-compose -f docker-compose.prod.yml up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to start..."
    sleep 15
    
    # Check if services are running
    if docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
        print_success "Production services are running!"
        echo ""
        echo "🌐 Website: https://$DOMAIN"
        echo "📊 Traefik Dashboard: https://$TRAEFIK_DOMAIN"
        echo ""
        echo "To view logs: docker-compose -f docker-compose.prod.yml logs -f"
        echo "To stop: docker-compose -f docker-compose.prod.yml down"
        echo ""
        print_warning "Note: SSL certificates may take a few minutes to be issued"
    else
        print_error "Some services failed to start. Check logs with: docker-compose -f docker-compose.prod.yml logs"
        exit 1
    fi
}

# Show usage
show_usage() {
    echo "Usage: $0 [local|production]"
    echo ""
    echo "Commands:"
    echo "  local       Deploy for local development"
    echo "  production  Deploy for production"
    echo ""
    echo "Examples:"
    echo "  $0 local"
    echo "  $0 production"
}

# Main script
main() {
    echo "🚀 Bright Soft Website Deployment Script"
    echo "========================================"
    echo ""
    
    # Check Docker installation
    check_docker
    
    # Create directories
    create_directories
    
    # Parse command line arguments
    case "${1:-}" in
        "local")
            deploy_local
            ;;
        "production")
            deploy_production
            ;;
        *)
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"

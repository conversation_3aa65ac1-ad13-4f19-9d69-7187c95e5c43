services:
  # Traefik reverse proxy
  traefik:
    image: traefik:v3.0
    container_name: traefik
    restart: unless-stopped
    command:
      # Enable Docker provider
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false

      # Enable API and dashboard
      - --api.dashboard=true
      - --api.insecure=false

      # Entry points
      - --entrypoints.web.address=:80
      - --entrypoints.traefik.address=:8080

      # Logging
      - --log.level=INFO
      - --accesslog=true
    ports:
      - "80:80"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    labels:
      # Enable Traefik for this service
      - traefik.enable=true

      # Dashboard configuration
      - traefik.http.routers.traefik.rule=Host(`traefik.brightsoft.local`)
      - traefik.http.routers.traefik.entrypoints=web
      - traefik.http.routers.traefik.service=api@internal

      # Dashboard authentication
      - traefik.http.routers.traefik.middlewares=auth
      - traefik.http.middlewares.auth.basicauth.users=admin:$$2y$$10$$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at9nm.fWSdHqa

      # Dashboard direct access via port 8080 (insecure)
      - traefik.http.routers.api.rule=Host(`localhost`) || Host(`127.0.0.1`)
      - traefik.http.routers.api.entrypoints=traefik
      - traefik.http.routers.api.service=api@internal
      - traefik.http.routers.api.middlewares=auth
    networks:
      - traefik

  # Bright Soft website
  brightsoft-website:
    image: nginx:alpine
    container_name: brightsoft-website
    restart: unless-stopped
    volumes:
      - ./:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
    labels:
      # Enable Traefik for this service
      - traefik.enable=true

      # HTTP router
      - traefik.http.routers.brightsoft.rule=Host(`brightsoft.local`) || Host(`www.brightsoft.local`)
      - traefik.http.routers.brightsoft.entrypoints=web

      # Service configuration
      - traefik.http.services.brightsoft.loadbalancer.server.port=80

      # Middleware for security headers
      - traefik.http.routers.brightsoft.middlewares=security-headers
      - traefik.http.middlewares.security-headers.headers.customresponseheaders.X-Frame-Options=DENY
      - traefik.http.middlewares.security-headers.headers.customresponseheaders.X-Content-Type-Options=nosniff
      - traefik.http.middlewares.security-headers.headers.customresponseheaders.Referrer-Policy=strict-origin-when-cross-origin
      - traefik.http.middlewares.security-headers.headers.customresponseheaders.Permissions-Policy=geolocation=(), microphone=(), camera=()
    networks:
      - traefik
    depends_on:
      - traefik

networks:
  traefik:
    external: false

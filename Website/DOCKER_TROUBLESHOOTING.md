# Docker Desktop Troubleshooting Guide

## 🚨 Current Issue
You're experiencing a Docker daemon connection error on Windows:
```
error during connect: Get "http://%2F%2F.%2Fpipe%2FdockerDesktopLinuxEngine/v1.48/...": 
open //./pipe/dockerDesktopLinuxEngine: The system cannot find the file specified.
```

## 🔧 Solution Steps

### Step 1: Restart Docker Desktop
1. **Close Docker Desktop completely**:
   - Right-click the Docker whale icon in system tray
   - Select "Quit Docker Desktop"
   - Wait for it to fully close

2. **Restart Docker Desktop**:
   - Open Docker Desktop from Start menu
   - Wait for the "Docker Desktop is starting..." message to complete
   - Look for the whale icon in system tray (should be solid, not blinking)

### Step 2: Verify Docker is Running
Open PowerShell or Command Prompt and run:
```bash
docker --version
docker info
```

If you still get errors, proceed to Step 3.

### Step 3: Reset Docker Desktop
1. **Open Docker Desktop Settings**:
   - Click the Docker whale icon in system tray
   - Select "Settings" or "Preferences"

2. **Reset to Factory Defaults**:
   - Go to "Troubleshoot" tab
   - Click "Reset to factory defaults"
   - Confirm the reset
   - Wait for Docker to restart

### Step 4: Alternative - Restart Docker Service
If Docker Desktop won't start properly:

1. **Open Services (Windows)**:
   - Press `Win + R`, type `services.msc`
   - Find "Docker Desktop Service"
   - Right-click and select "Restart"

2. **Or use PowerShell (Run as Administrator)**:
   ```powershell
   Restart-Service -Name "com.docker.service"
   ```

### Step 5: Check Windows Features
Ensure required Windows features are enabled:

1. **Open "Turn Windows features on or off"**:
   - Press `Win + R`, type `optionalfeatures`
   - Ensure these are checked:
     - ✅ Hyper-V
     - ✅ Windows Subsystem for Linux
     - ✅ Virtual Machine Platform

2. **Restart your computer** if you made changes

## 🧪 Test Docker After Fix

Once Docker Desktop is running properly, test with these commands:

```bash
# Navigate to your project directory
cd "d:\Roo Code\Website"

# Test Docker
docker --version
docker info

# Test Docker Compose
docker-compose --version

# Try pulling a simple image
docker pull hello-world
docker run hello-world
```

## 🚀 Deploy the Website

After Docker is working, deploy the website:

### Option 1: Using the Deploy Script
```bash
# Make sure you're in the project directory
cd "d:\Roo Code\Website"

# Run the deployment script
./deploy.sh local
```

### Option 2: Manual Docker Compose
```bash
# Navigate to project directory
cd "d:\Roo Code\Website"

# Start services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

## 📍 Expected Results

After successful deployment, you should see:
- ✅ Traefik container running on ports 80, 443, 8080
- ✅ Nginx container serving the website
- 🌐 Website accessible at: `http://brightsoft.local`
- 📊 Traefik dashboard at: `http://localhost:8080`

## 🔍 Additional Troubleshooting

### If Docker Desktop won't start:
1. **Check system requirements**:
   - Windows 10/11 Pro, Enterprise, or Education
   - Hyper-V and WSL2 support
   - Virtualization enabled in BIOS

2. **Update Docker Desktop**:
   - Download latest version from docker.com
   - Uninstall old version first if needed

3. **Check antivirus software**:
   - Some antivirus programs block Docker
   - Add Docker to exclusions

### If containers won't start:
1. **Check port conflicts**:
   ```bash
   netstat -an | findstr :80
   netstat -an | findstr :443
   netstat -an | findstr :8080
   ```

2. **Free up ports if needed**:
   - Stop IIS if running (uses port 80)
   - Stop other web servers

### If website doesn't load:
1. **Add hosts entries** (for local development):
   - Open `C:\Windows\System32\drivers\etc\hosts` as Administrator
   - Add these lines:
   ```
   127.0.0.1 brightsoft.local
   127.0.0.1 www.brightsoft.local
   127.0.0.1 traefik.brightsoft.local
   ```

2. **Check firewall settings**:
   - Allow Docker Desktop through Windows Firewall
   - Allow ports 80, 443, 8080

## 📞 Still Having Issues?

If you continue to have problems:

1. **Collect diagnostic information**:
   ```bash
   docker system info
   docker-compose config
   docker-compose logs
   ```

2. **Check Docker Desktop logs**:
   - Docker Desktop → Settings → Troubleshoot → "Show logs"

3. **Try alternative solutions**:
   - Use Docker Toolbox (legacy)
   - Use WSL2 with Docker Engine
   - Use a Linux VM

## ✅ Quick Verification Checklist

Before running docker-compose:
- [ ] Docker Desktop is running (whale icon in system tray)
- [ ] `docker --version` works
- [ ] `docker info` shows server information
- [ ] You're in the correct directory (`d:\Roo Code\Website`)
- [ ] Ports 80, 443, 8080 are available

---

**Need Help?** 
- Docker Desktop Documentation: https://docs.docker.com/desktop/
- Docker Community: https://forums.docker.com/

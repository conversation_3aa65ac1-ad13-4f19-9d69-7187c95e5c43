# Traefik Dashboard Login Fix

## Current Issue
The admin/password login is not working for the Traefik dashboard.

## Quick Solutions

### Option 1: Insecure Access (Immediate Fix)
The docker-compose.yml has been updated to use `--api.insecure=true`, which means:

**Access the dashboard without authentication:**
- URL: http://localhost:8090
- No username/password required

### Option 2: Working Authentication
I've updated the password hash in the docker-compose.yml file. Try these credentials:

**Login credentials:**
- Username: `admin`
- Password: `password`
- URL: http://localhost:8090

### Option 3: Generate New Password Hash
If the above doesn't work, generate a new hash:

```bash
# Using htpasswd (if available)
htpasswd -nb admin yourpassword

# Using Python
python3 -c "
import crypt
password = 'yourpassword'
salt = crypt.mksalt(crypt.METHOD_MD5)
hashed = crypt.crypt(password, salt)
docker_hash = hashed.replace('$', '$$')
print(f'admin:{docker_hash}')
"
```

## Steps to Apply Fix

1. **Restart the containers:**
   ```bash
   cd Website
   docker-compose down
   docker-compose up -d
   ```

2. **Check if containers are running:**
   ```bash
   docker-compose ps
   ```

3. **Access the dashboard:**
   - Go to http://localhost:8090
   - If insecure mode is enabled, no login required
   - If authentication is enabled, use admin/password

## Troubleshooting

### If you still can't access:

1. **Check Traefik logs:**
   ```bash
   docker-compose logs traefik
   ```

2. **Verify port is accessible:**
   ```bash
   curl http://localhost:8090
   ```

3. **Test with authentication:**
   ```bash
   curl -u admin:password http://localhost:8090/api/rawdata
   ```

### Common Issues:

1. **Container not running**: Check with `docker-compose ps`
2. **Port conflict**: Make sure port 8090 is free
3. **Wrong credentials**: Try without authentication first (insecure mode)
4. **Browser cache**: Try incognito/private mode

## Security Notes

- **Insecure mode** (`--api.insecure=true`) disables authentication entirely
- **For development**: Insecure mode is fine
- **For production**: Use proper authentication with strong passwords

## Current Configuration

The docker-compose.yml is now set to:
- **Insecure API**: `--api.insecure=true` (no authentication required)
- **Port**: 8090
- **Access**: http://localhost:8090

If you want to re-enable authentication, change `--api.insecure=true` to `--api.insecure=false` and restart the containers.

# Bright Soft - IT Consulting Website

A clean, modern, minimalist homepage for Bright Soft, a high-end IT consulting firm specializing in automation solutions.

## Features

### Design
- **Minimalist Design**: Clean layout with ample whitespace
- **Premium Typography**: Inter font family for modern, professional look
- **Color Scheme**: Neutral colors (white, black, grey) with blue accent (#2563eb)
- **Responsive**: Fully responsive design that works on all devices

### Sections
1. **Hero Section**: Award-winning engineer introduction with call-to-action
2. **Services**: PLC Programming, SCADA Systems, Software Automation
3. **Portfolio**: Featured projects with hover effects
4. **Contact**: Professional contact information and contact form
5. **Footer**: Company branding and navigation

### Animations & Interactions
- Smooth scrolling navigation
- Fade-in animations on scroll
- Hover effects on cards and buttons
- Parallax effect on hero section
- Counter animations for statistics
- 3D tilt effects on service cards
- Form submission feedback

## Technologies Used
- **HTML5**: Semantic markup
- **CSS3**: Modern styling with Flexbox and Grid
- **JavaScript**: Vanilla JS for interactions and animations
- **Google Fonts**: Inter font family

## File Structure
```
├── index.html          # Main HTML file
├── styles.css          # CSS styles and animations
├── script.js           # JavaScript interactions
├── README.md           # Project documentation
└── Asset/              # Images and content
    ├── Description.txt # Company information
    ├── Manufacturing.jpg
    ├── Marandoo.jpg
    ├── SO4.jpg
    └── TomraSorting.jpg
```

## Getting Started

1. **Clone or download** the project files
2. **Open** `index.html` in a web browser
3. **Customize** content in the HTML file as needed
4. **Replace** images in the Asset folder with your own

## Customization

### Colors
The main accent color can be changed by updating the CSS custom property:
```css
:root {
    --accent-color: #2563eb; /* Change this to your preferred color */
}
```

### Content
Update the following in `index.html`:
- Company name and branding
- Hero section text
- Services descriptions
- Portfolio project information
- Contact details

### Images
Replace the images in the `Asset/` folder with your own:
- Maintain aspect ratios for best results
- Optimize images for web (recommended: WebP format, <500KB each)

## Browser Support
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Performance Features
- Optimized CSS with minimal unused styles
- Efficient JavaScript with event delegation
- Lazy loading considerations for images
- Smooth animations with CSS transforms

## Contact Information
Based on the provided content:
- **Name**: Jackie Tran
- **Email**: <EMAIL>
- **Phone**: 0433 835 221
- **Location**: Western Australia

## License
This project is created for Bright Soft. All rights reserved.

---

*Made with Grace* - Bright Soft 2024

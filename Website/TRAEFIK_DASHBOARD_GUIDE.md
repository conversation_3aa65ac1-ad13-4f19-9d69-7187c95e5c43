# Traefik Dashboard Access Guide

## Problem: 403 Forbidden Error on Port 8080

The 403 Forbidden error occurs because <PERSON><PERSON><PERSON><PERSON>'s dashboard is configured with security settings that prevent direct access.

## Solutions

### Option 1: Quick Fix - Enable Insecure Access (Development Only)

**⚠️ Warning: Only use this for development/testing environments!**

In your `docker-compose.yml`, change:
```yaml
- --api.insecure=false
```
to:
```yaml
- --api.insecure=true
```

Then restart Traefik:
```bash
docker-compose down
docker-compose up -d
```

Access the dashboard at: http://localhost:8080

### Option 2: Secure Access with Authentication (Recommended)

#### Step 1: Generate Password Hash

Run the password generator:
```bash
cd Website
python3 generate_password.py
```

Or manually generate using htpasswd:
```bash
# Install apache2-utils if not available
sudo apt-get install apache2-utils

# Generate password hash
htpasswd -nb admin yourpassword
```

#### Step 2: Update Docker Compose

The current configuration includes basic authentication. The default credentials are:
- **Username**: admin
- **Password**: password

#### Step 3: Restart Services

```bash
docker-compose down
docker-compose up -d
```

#### Step 4: Access Dashboard

- **Via Port 8080**: http://localhost:8080 (with authentication)
- **Via Domain**: https://traefik.brightsoft.local (if DNS is configured)

## Current Configuration

Your docker-compose.yml is configured with:

1. **Secure API**: `--api.insecure=false`
2. **Dashboard enabled**: `--api.dashboard=true`
3. **Basic Authentication**: Username `admin`, Password `password`
4. **Multiple Access Methods**:
   - Port 8080 with authentication
   - Domain-based access with HTTPS

## Access Methods

### Method 1: Direct Port Access
- URL: http://localhost:8080
- Authentication: admin/password
- Security: Basic Auth over HTTP

### Method 2: Domain Access (Production)
- URL: https://traefik.brightsoft.local
- Authentication: admin/password
- Security: Basic Auth over HTTPS with Let's Encrypt

## Troubleshooting

### Still Getting 403 Error?

1. **Check if containers are running**:
   ```bash
   docker-compose ps
   ```

2. **Check Traefik logs**:
   ```bash
   docker-compose logs traefik
   ```

3. **Verify authentication**:
   - Make sure you're using the correct username/password
   - Check if the password hash is correctly formatted

4. **Test with curl**:
   ```bash
   curl -u admin:password http://localhost:8080/api/rawdata
   ```

### Common Issues

1. **Wrong credentials**: Double-check username and password
2. **Container not running**: Restart with `docker-compose up -d`
3. **Port conflict**: Make sure port 8080 isn't used by another service
4. **Network issues**: Check if you can access other services

### Logs to Check

```bash
# Traefik logs
docker-compose logs traefik

# All services logs
docker-compose logs

# Follow logs in real-time
docker-compose logs -f traefik
```

## Security Notes

- The current setup uses HTTP for port 8080 access (development)
- For production, use the HTTPS domain access method
- Consider using stronger passwords than the default
- Regularly update Traefik to the latest version

## Next Steps

1. Test the dashboard access with the provided credentials
2. If working, consider changing the default password
3. Set up proper DNS for domain-based access in production
4. Configure additional security headers if needed

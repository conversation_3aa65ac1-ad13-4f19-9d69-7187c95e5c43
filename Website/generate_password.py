#!/usr/bin/env python3
"""
Generate password hash for Traefik basic authentication

This script generates a bcrypt hash that can be used in Traefik's
basic authentication configuration.
"""

import bcrypt
import getpass

def generate_password_hash():
    """Generate a bcrypt hash for a password"""
    # Get username
    username = input("Enter username (default: admin): ").strip()
    if not username:
        username = "admin"
    
    # Get password
    password = getpass.getpass("Enter password: ")
    if not password:
        print("Password cannot be empty!")
        return
    
    # Generate bcrypt hash
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    
    # Format for docker-compose (escape $ signs)
    hash_str = hashed.decode('utf-8')
    docker_hash = hash_str.replace('$', '$$')
    
    print(f"\nGenerated hash for user '{username}':")
    print(f"Raw hash: {hash_str}")
    print(f"Docker-compose format: {username}:{docker_hash}")
    print(f"\nAdd this to your docker-compose.yml:")
    print(f"- traefik.http.middlewares.auth.basicauth.users={username}:{docker_hash}")

if __name__ == "__main__":
    try:
        generate_password_hash()
    except KeyboardInterrupt:
        print("\nOperation cancelled.")
    except Exception as e:
        print(f"Error: {e}")

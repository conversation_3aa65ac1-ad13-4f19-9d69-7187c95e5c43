#!/usr/bin/env python3
"""
Generate password hash for Traefik basic authentication

This script generates a password hash that can be used in Traefik's
basic authentication configuration using the standard crypt library.
"""

import crypt
import getpass
import secrets
import string

def generate_password_hash():
    """Generate a password hash for Traefik basic auth"""
    # Get username
    username = input("Enter username (default: admin): ").strip()
    if not username:
        username = "admin"

    # Get password
    password = getpass.getpass("Enter password: ")
    if not password:
        print("Password cannot be empty!")
        return

    # Generate salt and hash using MD5 (widely supported)
    try:
        # Try SHA-512 first (more secure)
        salt = crypt.mksalt(crypt.METHOD_SHA512)
        hashed = crypt.crypt(password, salt)
    except:
        try:
            # Fall back to SHA-256
            salt = crypt.mksalt(crypt.METHOD_SHA256)
            hashed = crypt.crypt(password, salt)
        except:
            # Fall back to MD5 (least secure but most compatible)
            salt = crypt.mksalt(crypt.METHOD_MD5)
            hashed = crypt.crypt(password, salt)

    # Format for docker-compose (escape $ signs)
    docker_hash = hashed.replace('$', '$$')

    print(f"\nGenerated hash for user '{username}':")
    print(f"Raw hash: {hashed}")
    print(f"Docker-compose format: {username}:{docker_hash}")
    print(f"\nAdd this to your docker-compose.yml:")
    print(f"- traefik.http.middlewares.auth.basicauth.users={username}:{docker_hash}")

def generate_simple_hash():
    """Generate a simple hash using htpasswd format"""
    import base64
    import hashlib

    username = "admin"
    password = "password"

    # Create SHA1 hash (htpasswd format)
    sha1_hash = hashlib.sha1(password.encode()).digest()
    b64_hash = base64.b64encode(sha1_hash).decode()
    htpasswd_format = f"{username}:{{SHA}}{b64_hash}"
    docker_format = htpasswd_format.replace('$', '$$')

    print(f"Simple hash for {username}:{password}")
    print(f"htpasswd format: {htpasswd_format}")
    print(f"Docker format: {docker_format}")

    return docker_format

if __name__ == "__main__":
    try:
        print("Choose an option:")
        print("1. Generate custom password hash")
        print("2. Generate simple hash for admin:password")
        choice = input("Enter choice (1 or 2, default: 2): ").strip()

        if choice == "1":
            generate_password_hash()
        else:
            generate_simple_hash()

    except KeyboardInterrupt:
        print("\nOperation cancelled.")
    except Exception as e:
        print(f"Error: {e}")

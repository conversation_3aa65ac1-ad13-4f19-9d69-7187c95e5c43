#!/bin/bash

# Setup script for the MCP server

echo "Setting up the MCP server..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is required but not installed. Please install Python 3 and try again."
    exit 1
fi

# Create a virtual environment
echo "Creating a virtual environment..."
python3 -m venv venv

# Activate the virtual environment
echo "Activating the virtual environment..."
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

echo "Setup complete!"
echo ""
echo "To run the server, use one of the following commands:"
echo ""
echo "Basic server:"
echo "python mcp_server.py"
echo ""
echo "Advanced server:"
echo "python advanced_mcp_server.py"
echo ""
echo "Development mode with MCP Inspector:"
echo "mcp dev mcp_server.py"
echo ""
echo "Install in Claude Desktop:"
echo "mcp install mcp_server.py"
echo ""
echo "Run tests:"
echo "python test_server.py"

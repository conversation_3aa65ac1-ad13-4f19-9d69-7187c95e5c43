"""
Test script for the MCP server

This script tests the basic functionality of the MCP server.
"""

import asyncio
import json
import os
import tempfile
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_resources(session):
    """Test the resources functionality"""
    print("Testing resources...")
    
    # Test system info resource
    content, mime_type = await session.read_resource("system://info")
    print(f"System info: {content[:100]}...")
    assert mime_type == "text/plain", f"Expected mime_type 'text/plain', got '{mime_type}'"
    
    # Test current time resource
    content, mime_type = await session.read_resource("time://current")
    print(f"Current time: {content}")
    assert mime_type == "text/plain", f"Expected mime_type 'text/plain', got '{mime_type}'"
    
    # Test file resource
    with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp:
        temp.write("Test file content")
        temp_path = temp.name
    
    try:
        content, mime_type = await session.read_resource(f"file://{temp_path}")
        print(f"File content: {content}")
        assert content == "Test file content", f"Expected 'Test file content', got '{content}'"
    finally:
        os.unlink(temp_path)
    
    # Test directory resource
    content, mime_type = await session.read_resource(f"directory://{os.getcwd()}")
    print(f"Directory content sample: {content[:100]}...")
    
    print("Resources tests passed!\n")

async def test_tools(session):
    """Test the tools functionality"""
    print("Testing tools...")
    
    # Test calculate tool
    result = await session.call_tool("calculate", arguments={"expression": "2 + 2 * 3"})
    print(f"Calculate result: {result}")
    assert "Result: 8" in result, f"Expected 'Result: 8', got '{result}'"
    
    # Test generate_random tool
    result = await session.call_tool("generate_random", arguments={"min_value": 1, "max_value": 10})
    print(f"Random number: {result}")
    assert 1 <= result <= 10, f"Expected number between 1 and 10, got {result}"
    
    # Test write_file tool
    with tempfile.NamedTemporaryFile(delete=False) as temp:
        temp_path = temp.name
    
    try:
        result = await session.call_tool("write_file", arguments={
            "path": temp_path,
            "content": "Test content"
        })
        print(f"Write file result: {result}")
        
        with open(temp_path, 'r') as f:
            content = f.read()
        assert content == "Test content", f"Expected 'Test content', got '{content}'"
    finally:
        os.unlink(temp_path)
    
    print("Tools tests passed!\n")

async def test_prompts(session):
    """Test the prompts functionality"""
    print("Testing prompts...")
    
    # Test simple prompt
    prompt_result = await session.get_prompt("simple_prompt", arguments={"message": "Hello, world!"})
    print(f"Simple prompt: {prompt_result.messages[0].content.text}")
    assert prompt_result.messages[0].content.text == "Hello, world!", \
        f"Expected 'Hello, world!', got '{prompt_result.messages[0].content.text}'"
    
    # Test analyze_code prompt
    prompt_result = await session.get_prompt("analyze_code", arguments={"code": "print('Hello')"})
    print(f"Analyze code prompt sample: {prompt_result.messages[0].content.text[:50]}...")
    assert "analyze this code" in prompt_result.messages[0].content.text.lower(), \
        f"Expected prompt to contain 'analyze this code', got '{prompt_result.messages[0].content.text}'"
    
    print("Prompts tests passed!\n")

async def main():
    """Main test function"""
    # Create server parameters
    server_params = StdioServerParameters(
        command="python",
        args=["mcp_server.py"],
        env=None,
    )

    # Connect to the server
    print("Connecting to server...")
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize the connection
            await session.initialize()
            print("Connected to server")
            
            # Run tests
            await test_resources(session)
            await test_tools(session)
            await test_prompts(session)
            
            print("All tests passed!")

if __name__ == "__main__":
    asyncio.run(main())

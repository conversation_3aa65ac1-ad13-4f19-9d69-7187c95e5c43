#!/bin/bash

# Setup script for the MCP server using conda

echo "Setting up the MCP server with conda..."

# Check if conda is installed
if ! command -v conda &> /dev/null; then
    echo "Conda is required but not installed. Please install Conda and try again."
    echo "You can download it from: https://docs.conda.io/en/latest/miniconda.html"
    exit 1
fi

# Create a conda environment
echo "Creating a conda environment..."
conda create -y -n mcp-server python=3.9

# Activate the conda environment
echo "Activating the conda environment..."
source "$(conda info --base)/etc/profile.d/conda.sh"
conda activate mcp-server

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

echo "Setup complete!"
echo ""
echo "To activate the conda environment in the future, run:"
echo "conda activate mcp-server"
echo ""
echo "To run the server, use one of the following commands:"
echo ""
echo "Basic server:"
echo "python mcp_server.py"
echo ""
echo "Advanced server:"
echo "python advanced_mcp_server.py"
echo ""
echo "Development mode with MCP Inspector:"
echo "mcp dev mcp_server.py"
echo ""
echo "Install in Claude Desktop:"
echo "mcp install mcp_server.py"
echo ""
echo "Run tests:"
echo "python test_server.py"

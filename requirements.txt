# Python dependencies for the MCP Server
# ----------------------------------------

# Core dependencies
mcp[cli]>=1.0.0  # Model Context Protocol SDK with CLI tools
requests>=2.25.0  # For HTTP requests

# For the advanced server with image processing
Pillow>=9.0.0  # For image manipulation

# ----------------------------------------
# Conda Environment Setup
# ----------------------------------------
# To create a conda environment for this project:
#
# 1. Create a new conda environment:
#    conda create -n mcp-server python=3.9
#
# 2. Activate the environment:
#    conda activate mcp-server
#
# 3. Install pip packages:
#    pip install -r requirements.txt
#
# Note: Some packages like mcp might not be available in conda channels,
# so we use pip to install them within the conda environment.

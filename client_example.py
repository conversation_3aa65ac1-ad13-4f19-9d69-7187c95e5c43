"""
Example MCP Client

This script demonstrates how to use the MCP client to connect to our server.
"""

import asyncio
from mcp import Client<PERSON>ession, StdioServerParameters, types
from mcp.client.stdio import stdio_client

async def main():
    # Create server parameters for stdio connection
    server_params = StdioServerParameters(
        command="python",  # Executable
        args=["mcp_server.py"],  # Server script
        env=None,  # Optional environment variables
    )

    # Connect to the server
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize the connection
            await session.initialize()
            print("Connected to server")

            # List available resources
            resources = await session.list_resources()
            print("\nAvailable Resources:")
            for resource in resources:
                print(f"- {resource.name}: {resource.description}")

            # List available tools
            tools = await session.list_tools()
            print("\nAvailable Tools:")
            for tool in tools:
                print(f"- {tool.name}: {tool.description}")
                print(f"  Arguments: {[arg.name for arg in tool.arguments]}")

            # List available prompts
            prompts = await session.list_prompts()
            print("\nAvailable Prompts:")
            for prompt in prompts:
                print(f"- {prompt.name}: {prompt.description}")

            # Read a resource
            print("\nReading system info resource:")
            content, mime_type = await session.read_resource("system://info")
            print(f"Content: {content}")
            print(f"MIME Type: {mime_type}")

            # Call a tool
            print("\nCalling calculate tool:")
            result = await session.call_tool("calculate", arguments={"expression": "2 + 2"})
            print(f"Result: {result}")

            # Get a prompt
            print("\nGetting analyze_code prompt:")
            prompt_result = await session.get_prompt(
                "analyze_code", arguments={"code": "print('Hello, world!')"}
            )
            print(f"Prompt: {prompt_result.messages[0].content.text}")

if __name__ == "__main__":
    asyncio.run(main())

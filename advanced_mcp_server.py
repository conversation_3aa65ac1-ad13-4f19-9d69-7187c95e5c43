"""
Advanced Local Model Context Protocol Server

This server implements more advanced features of the Model Context Protocol (MCP)
including image handling, complex data structures, and more.
"""

from mcp.server.fastmcp import FastMCP, Context, Image
from mcp.server.fastmcp.prompts import base
from dataclasses import dataclass
from typing import Dict, List, Optional, Union
import asyncio
import datetime
import json
import os
import platform
import random
import requests
from PIL import Image as PILImage, ImageDraw, ImageFont

# Create an MCP server
mcp = FastMCP("Advanced Context Server")

# ---- DATA MODELS ----

@dataclass
class WeatherData:
    temperature: float
    humidity: float
    conditions: str
    location: str
    timestamp: str

@dataclass
class UserProfile:
    username: str
    email: str
    created_at: str
    preferences: Dict[str, str]

# ---- RESOURCES ----

@mcp.resource("weather://{location}")
def get_weather(location: str) -> str:
    """Get weather information for a location (simulated)"""
    # This is a simulated weather API response
    weather = WeatherData(
        temperature=random.uniform(0, 35),
        humidity=random.uniform(30, 90),
        conditions=random.choice(["Sunny", "Cloudy", "Rainy", "Snowy"]),
        location=location,
        timestamp=datetime.datetime.now().isoformat()
    )
    
    return json.dumps({
        "temperature": weather.temperature,
        "humidity": weather.humidity,
        "conditions": weather.conditions,
        "location": weather.location,
        "timestamp": weather.timestamp
    }, indent=2)

@mcp.resource("user://{username}")
def get_user_profile(username: str) -> str:
    """Get a user profile (simulated)"""
    # This is a simulated user profile
    profile = UserProfile(
        username=username,
        email=f"{username}@example.com",
        created_at=datetime.datetime.now().isoformat(),
        preferences={
            "theme": random.choice(["light", "dark"]),
            "language": random.choice(["en", "es", "fr", "de"]),
            "notifications": random.choice(["all", "important", "none"])
        }
    )
    
    return json.dumps({
        "username": profile.username,
        "email": profile.email,
        "created_at": profile.created_at,
        "preferences": profile.preferences
    }, indent=2)

# ---- TOOLS ----

@mcp.tool()
def generate_chart(title: str, data_points: List[float]) -> Image:
    """Generate a simple chart image from data points"""
    # Create a simple bar chart image
    width, height = 500, 300
    img = PILImage.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(img)
    
    # Draw title
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except IOError:
        font = ImageFont.load_default()
    
    draw.text((10, 10), title, fill='black', font=font)
    
    # Draw bars
    if data_points:
        max_value = max(data_points)
        bar_width = width // (len(data_points) + 1)
        scale_factor = (height - 60) / max(max_value, 1)
        
        for i, value in enumerate(data_points):
            bar_height = value * scale_factor
            x0 = (i + 1) * bar_width
            y0 = height - 30 - bar_height
            x1 = x0 + bar_width // 2
            y1 = height - 30
            
            draw.rectangle([x0, y0, x1, y1], fill='blue', outline='black')
            draw.text((x0, y1 + 5), str(round(value, 1)), fill='black', font=font)
    
    # Convert to bytes
    import io
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format='PNG')
    
    return Image(data=img_byte_arr.getvalue(), format="png")

@mcp.tool()
async def analyze_text(text: str, ctx: Context) -> Dict[str, Union[int, List[str]]]:
    """Analyze text and return statistics"""
    # Simulate a long-running operation
    await ctx.report_progress(0, 3)
    ctx.info("Starting text analysis...")
    await asyncio.sleep(0.5)
    
    # Word count
    words = text.split()
    word_count = len(words)
    await ctx.report_progress(1, 3)
    ctx.info(f"Counted {word_count} words")
    await asyncio.sleep(0.5)
    
    # Find unique words
    unique_words = list(set(words))
    await ctx.report_progress(2, 3)
    ctx.info(f"Found {len(unique_words)} unique words")
    await asyncio.sleep(0.5)
    
    # Find most common words
    word_freq = {}
    for word in words:
        word = word.lower().strip(".,!?;:()")
        if word:
            word_freq[word] = word_freq.get(word, 0) + 1
    
    common_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:5]
    common_word_list = [f"{word} ({count})" for word, count in common_words]
    
    await ctx.report_progress(3, 3)
    ctx.info("Analysis complete")
    
    return {
        "word_count": word_count,
        "unique_word_count": len(unique_words),
        "common_words": common_word_list
    }

@mcp.tool()
def search_database(query: str, limit: int = 5) -> List[Dict[str, str]]:
    """Simulate searching a database"""
    # This is a simulated database search
    results = []
    for i in range(limit):
        results.append({
            "id": f"result-{i+1}",
            "title": f"Search result for '{query}' #{i+1}",
            "snippet": f"This is a simulated search result for the query '{query}'.",
            "url": f"https://example.com/results/{i+1}"
        })
    
    return results

# ---- PROMPTS ----

@mcp.prompt()
def data_analysis_prompt(data: str) -> List[base.Message]:
    """Prompt for analyzing data"""
    return [
        base.SystemMessage("You are a data analysis expert. Analyze the following data and provide insights."),
        base.UserMessage(f"Please analyze this data:\n\n{data}"),
    ]

@mcp.prompt()
def image_description_prompt() -> List[base.Message]:
    """Prompt for describing an image"""
    return [
        base.SystemMessage("You are an image description expert."),
        base.UserMessage("Please describe this image in detail, including what you see and any notable elements."),
    ]

@mcp.prompt()
def brainstorming_session(topic: str, num_ideas: int = 5) -> str:
    """Prompt for a brainstorming session"""
    return f"""Let's brainstorm {num_ideas} creative ideas about {topic}.

For each idea, please provide:
1. A catchy title
2. A brief description (2-3 sentences)
3. Potential benefits
4. Possible challenges

Be creative and think outside the box!
"""

# Run the server if executed directly
if __name__ == "__main__":
    mcp.run()

# Extending the MCP Server

This guide provides detailed instructions on how to extend the MCP server with your own custom functionality.

## Adding Custom Resources

Resources in MCP are used to provide contextual data to LLMs. They're similar to GET endpoints in a REST API.

### Basic Resource

```python
@mcp.resource("my-resource://static")
def my_static_resource() -> str:
    """My static resource description"""
    return "This is a static resource"
```

### Parameterized Resource

```python
@mcp.resource("my-resource://{param}")
def my_parameterized_resource(param: str) -> str:
    """Resource that takes a parameter"""
    return f"Resource with parameter: {param}"
```

### Multiple Parameters

```python
@mcp.resource("my-resource://{category}/{id}")
def my_multi_param_resource(category: str, id: str) -> str:
    """Resource with multiple parameters"""
    return f"Resource for category {category} with ID {id}"
```

### Binary Resources

For binary data like images:

```python
from mcp.server.fastmcp import Image
from PIL import Image as PILImage

@mcp.resource("image://{name}")
def get_image(name: str) -> Image:
    """Get an image resource"""
    img = PILImage.open(f"{name}.png")
    return Image(data=img.tobytes(), format="png")
```

## Adding Custom Tools

Tools are functions that LLMs can call to perform actions or computations.

### Basic Tool

```python
@mcp.tool()
def my_tool(param: str) -> str:
    """My tool description"""
    return f"Tool executed with parameter: {param}"
```

### Tool with Multiple Parameters

```python
@mcp.tool()
def advanced_tool(param1: str, param2: int, optional_param: bool = False) -> str:
    """Tool with multiple parameters"""
    result = f"Processing {param1} with {param2}"
    if optional_param:
        result += " (optional parameter enabled)"
    return result
```

### Asynchronous Tool

```python
@mcp.tool()
async def async_tool(param: str) -> str:
    """Asynchronous tool"""
    import asyncio
    await asyncio.sleep(1)  # Simulate async work
    return f"Async result for {param}"
```

### Tool with Progress Reporting

```python
from mcp.server.fastmcp import Context

@mcp.tool()
async def long_running_tool(steps: int, ctx: Context) -> str:
    """Tool with progress reporting"""
    for i in range(steps):
        # Report progress
        await ctx.report_progress(i, steps)
        # Log information
        ctx.info(f"Step {i+1}/{steps} completed")
        # Simulate work
        import asyncio
        await asyncio.sleep(0.5)
    return f"Completed {steps} steps"
```

### Tool Returning Complex Data

```python
from typing import Dict, List

@mcp.tool()
def data_tool() -> Dict[str, List[str]]:
    """Tool returning complex data"""
    return {
        "categories": ["A", "B", "C"],
        "items": ["item1", "item2", "item3"]
    }
```

## Adding Custom Prompts

Prompts are templates for LLM interactions.

### Basic Prompt

```python
@mcp.prompt()
def my_prompt(param: str) -> str:
    """Simple prompt description"""
    return f"This is a prompt with parameter: {param}"
```

### Multi-Message Prompt

```python
from mcp.server.fastmcp.prompts import base
from typing import List

@mcp.prompt()
def conversation_prompt(topic: str) -> List[base.Message]:
    """Multi-message conversation prompt"""
    return [
        base.SystemMessage("You are a helpful assistant."),
        base.UserMessage(f"Let's talk about {topic}."),
        base.AssistantMessage("I'd be happy to discuss this topic with you."),
    ]
```

## Advanced Server Configuration

### Server with Lifespan

```python
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
from dataclasses import dataclass
from mcp.server.fastmcp import FastMCP

@dataclass
class AppContext:
    db: object  # Replace with your actual DB type

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[AppContext]:
    """Manage application lifecycle with type-safe context"""
    # Initialize on startup
    db = await connect_to_database()
    try:
        yield AppContext(db=db)
    finally:
        # Cleanup on shutdown
        await db.disconnect()

# Pass lifespan to server
mcp = FastMCP("My App", lifespan=app_lifespan)

# Access lifespan context in tools
@mcp.tool()
def query_db(ctx: Context) -> str:
    """Tool that uses initialized resources"""
    db = ctx.request_context.lifespan_context.db
    return db.query()
```

### Server with Dependencies

```python
# Specify dependencies for deployment and development
mcp = FastMCP("My App", dependencies=["pandas", "numpy"])
```

## Integration with External Services

### Database Integration

```python
import sqlite3

@mcp.tool()
def query_database(sql: str) -> str:
    """Execute SQL queries safely"""
    conn = sqlite3.connect("database.db")
    try:
        result = conn.execute(sql).fetchall()
        return "\n".join(str(row) for row in result)
    except Exception as e:
        return f"Error: {str(e)}"
```

### API Integration

```python
import requests

@mcp.tool()
async def fetch_api_data(endpoint: str) -> dict:
    """Fetch data from an external API"""
    try:
        response = requests.get(f"https://api.example.com/{endpoint}")
        response.raise_for_status()
        return response.json()
    except Exception as e:
        return {"error": str(e)}
```

## Running Your Extended Server

After adding your custom functionality, you can run your server using:

```bash
python your_server.py
```

Or use the MCP CLI tools:

```bash
# Development mode
mcp dev your_server.py

# Install in Claude Desktop
mcp install your_server.py
```

"""
Local Model Context Protocol Server

This server implements the Model Context Protocol (MCP) to provide resources,
tools, and prompts to LLM applications.
"""

from mcp.server.fastmcp import FastMCP, Context, Image
from mcp.server.fastmcp.prompts import base
import datetime
import json
import os
import platform
import random
import requests
from typing import Dict, List, Optional, Union

# Create an MCP server
mcp = FastMCP("Local Context Server")

# ---- RESOURCES ----

@mcp.resource("system://info")
def get_system_info() -> str:
    """Get basic system information"""
    info = {
        "os": platform.system(),
        "os_version": platform.version(),
        "python_version": platform.python_version(),
        "hostname": platform.node(),
        "processor": platform.processor(),
        "timestamp": datetime.datetime.now().isoformat()
    }
    return json.dumps(info, indent=2)

@mcp.resource("time://current")
def get_current_time() -> str:
    """Get the current time"""
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

@mcp.resource("file://{path}")
def get_file_content(path: str) -> str:
    """Get the content of a file"""
    try:
        with open(path, "r") as f:
            return f.read()
    except Exception as e:
        return f"Error reading file: {str(e)}"

@mcp.resource("directory://{path}")
def list_directory(path: str) -> str:
    """List the contents of a directory"""
    try:
        items = os.listdir(path)
        result = []
        for item in items:
            full_path = os.path.join(path, item)
            item_type = "directory" if os.path.isdir(full_path) else "file"
            size = os.path.getsize(full_path) if os.path.isfile(full_path) else None
            result.append({
                "name": item,
                "type": item_type,
                "size": size
            })
        return json.dumps(result, indent=2)
    except Exception as e:
        return f"Error listing directory: {str(e)}"

# ---- TOOLS ----

@mcp.tool()
def calculate(expression: str) -> str:
    """Calculate the result of a mathematical expression"""
    try:
        # Using eval is generally not recommended for security reasons,
        # but this is a simple example
        result = eval(expression, {"__builtins__": {}})
        return f"Result: {result}"
    except Exception as e:
        return f"Error calculating expression: {str(e)}"

@mcp.tool()
def fetch_url(url: str) -> str:
    """Fetch the content of a URL"""
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.text[:1000] + "..." if len(response.text) > 1000 else response.text
    except Exception as e:
        return f"Error fetching URL: {str(e)}"

@mcp.tool()
def write_file(path: str, content: str) -> str:
    """Write content to a file"""
    try:
        with open(path, "w") as f:
            f.write(content)
        return f"Successfully wrote to {path}"
    except Exception as e:
        return f"Error writing to file: {str(e)}"

@mcp.tool()
def generate_random(min_value: int = 1, max_value: int = 100) -> int:
    """Generate a random number between min_value and max_value"""
    return random.randint(min_value, max_value)

@mcp.tool()
async def long_operation(steps: int, ctx: Context) -> str:
    """Simulate a long operation with progress reporting"""
    for i in range(steps):
        # Report progress
        await ctx.report_progress(i, steps)
        # Add a log message
        ctx.info(f"Completed step {i+1} of {steps}")
        # Simulate work
        import asyncio
        await asyncio.sleep(0.5)
    
    return f"Completed {steps} steps"

# ---- PROMPTS ----

@mcp.prompt()
def simple_prompt(message: str) -> str:
    """A simple prompt that returns the message"""
    return message

@mcp.prompt()
def analyze_code(code: str) -> str:
    """Prompt for analyzing code"""
    return f"""Please analyze this code and provide feedback:

```
{code}
```

Focus on:
1. Code quality
2. Potential bugs
3. Performance issues
4. Security concerns
"""

@mcp.prompt()
def conversation_starter(topic: str) -> List[base.Message]:
    """Start a conversation about a topic"""
    return [
        base.SystemMessage("You are a helpful assistant specializing in discussing various topics."),
        base.UserMessage(f"I'd like to discuss {topic}. What are some interesting aspects of this topic?"),
        base.AssistantMessage("I'd be happy to discuss this topic with you. Let me think about some interesting aspects..."),
    ]

# Run the server if executed directly
if __name__ == "__main__":
    mcp.run()

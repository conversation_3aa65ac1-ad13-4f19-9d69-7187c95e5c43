"""
Local Model Context Protocol Server
==================================

This server implements the Model Context Protocol (MCP) to provide resources,
tools, and prompts to LLM applications.

The Model Context Protocol (MCP) is an open protocol that enables seamless
integration between LLM applications and external data sources and tools.

This server provides:
1. Resources - Contextual data that can be loaded into an LLM's context
2. Tools - Functions that can be executed by the LLM
3. Prompts - Templated interactions for the LLM

Usage:
------
Run directly:
    python mcp_server.py

Development mode:
    mcp dev mcp_server.py

Install in Claude Desktop:
    mcp install mcp_server.py
"""

# Standard library imports
import datetime
import json
import os
import platform
import random

# Third-party imports
import requests

# MCP-specific imports
from mcp.server.fastmcp import FastMCP, Context
from mcp.server.fastmcp.prompts import base
from typing import List

# ========================================================================
# SERVER INITIALIZATION
# ========================================================================

# Create an MCP server with a descriptive name
# The name will be displayed to users in Claude Desktop or other MCP clients
mcp = FastMCP(
    "Local Context Server",  # Server name
    # Optional: specify dependencies for deployment
    # dependencies=["requests", "pillow"],
)

# ========================================================================
# RESOURCES
# ========================================================================
# Resources are like GET endpoints in a REST API - they provide data but
# shouldn't perform significant computation or have side effects.
#
# Resource URIs follow the format: "scheme://path" or "scheme://{param}"
# where {param} is a placeholder for a parameter value.

@mcp.resource("system://info")
def get_system_info() -> str:
    """Get basic system information

    This resource provides information about the system where the server is running,
    including OS details, Python version, and hardware information.

    Returns:
        str: JSON-formatted string containing system information
    """
    # Collect system information using the platform module
    info = {
        "os": platform.system(),              # Operating system name (e.g., 'Windows', 'Linux', 'Darwin')
        "os_version": platform.version(),     # OS version information
        "python_version": platform.python_version(),  # Python version
        "hostname": platform.node(),          # Network hostname
        "processor": platform.processor(),    # Processor information
        "timestamp": datetime.datetime.now().isoformat()  # Current timestamp
    }
    # Return the information as a formatted JSON string
    return json.dumps(info, indent=2)

@mcp.resource("time://current")
def get_current_time() -> str:
    """Get the current time

    This resource provides the current date and time in a human-readable format.

    Returns:
        str: Current date and time in YYYY-MM-DD HH:MM:SS format
    """
    # Format the current datetime as a string
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

@mcp.resource("file://{path}")
def get_file_content(path: str) -> str:
    """Get the content of a file

    This resource reads and returns the content of a file at the specified path.

    Args:
        path (str): The path to the file to read

    Returns:
        str: The content of the file, or an error message if the file cannot be read

    Example:
        file:///path/to/file.txt
    """
    try:
        # Open the file in read mode and return its contents
        with open(path, "r") as f:
            return f.read()
    except Exception as e:
        # Return an error message if the file cannot be read
        return f"Error reading file: {str(e)}"

@mcp.resource("directory://{path}")
def list_directory(path: str) -> str:
    """List the contents of a directory

    This resource lists all files and directories at the specified path,
    including their types and sizes (for files).

    Args:
        path (str): The path to the directory to list

    Returns:
        str: JSON-formatted string containing directory contents, or an error message

    Example:
        directory:///path/to/folder
    """
    try:
        # Get list of all items in the directory
        items = os.listdir(path)
        result = []

        # Process each item to get its type and size
        for item in items:
            # Construct the full path to the item
            full_path = os.path.join(path, item)

            # Determine if it's a file or directory
            item_type = "directory" if os.path.isdir(full_path) else "file"

            # Get file size (only for files)
            size = os.path.getsize(full_path) if os.path.isfile(full_path) else None

            # Add item information to the result list
            result.append({
                "name": item,
                "type": item_type,
                "size": size
            })

        # Return the result as a formatted JSON string
        return json.dumps(result, indent=2)
    except Exception as e:
        # Return an error message if the directory cannot be listed
        return f"Error listing directory: {str(e)}"

# ========================================================================
# TOOLS
# ========================================================================
# Tools are functions that LLMs can call to perform actions or computations.
# Unlike resources, tools are expected to perform computation and can have
# side effects. They're similar to POST endpoints in a REST API.

@mcp.tool()
def calculate(expression: str) -> str:
    """Calculate the result of a mathematical expression

    This tool evaluates a mathematical expression and returns the result.
    For security reasons, it uses a restricted environment for evaluation.

    Args:
        expression (str): The mathematical expression to evaluate (e.g., "2 + 2 * 3")

    Returns:
        str: The result of the calculation, or an error message if evaluation fails

    Example:
        calculate("2 + 2") -> "Result: 4"
    """
    try:
        # Using eval is generally not recommended for security reasons,
        # but this is a simple example. We restrict the available builtins
        # to prevent code execution vulnerabilities.
        restricted_env = {"__builtins__": {}}
        result = eval(expression, restricted_env)
        return f"Result: {result}"
    except Exception as e:
        # Return an error message if evaluation fails
        return f"Error calculating expression: {str(e)}"

@mcp.tool()
def fetch_url(url: str) -> str:
    """Fetch the content of a URL

    This tool retrieves the content of a web page at the specified URL.
    For readability, the content is truncated to 1000 characters if it's too long.

    Args:
        url (str): The URL to fetch (e.g., "https://example.com")

    Returns:
        str: The content of the web page, or an error message if fetching fails

    Example:
        fetch_url("https://example.com") -> "<!doctype html>..."
    """
    try:
        # Send an HTTP GET request to the URL
        response = requests.get(url)

        # Raise an exception for 4XX/5XX responses
        response.raise_for_status()

        # Truncate the response if it's too long
        if len(response.text) > 1000:
            return response.text[:1000] + "..."
        else:
            return response.text
    except Exception as e:
        # Return an error message if fetching fails
        return f"Error fetching URL: {str(e)}"

@mcp.tool()
def write_file(path: str, content: str) -> str:
    """Write content to a file

    This tool writes the specified content to a file at the given path.
    If the file already exists, it will be overwritten.

    Args:
        path (str): The path where the file should be written
        content (str): The content to write to the file

    Returns:
        str: A success message, or an error message if writing fails

    Example:
        write_file("/path/to/file.txt", "Hello, world!") -> "Successfully wrote to /path/to/file.txt"
    """
    try:
        # Open the file in write mode (creates the file if it doesn't exist,
        # or truncates it if it does)
        with open(path, "w") as f:
            f.write(content)
        return f"Successfully wrote to {path}"
    except Exception as e:
        # Return an error message if writing fails
        return f"Error writing to file: {str(e)}"

@mcp.tool()
def generate_random(min_value: int = 1, max_value: int = 100) -> int:
    """Generate a random number between min_value and max_value

    This tool generates a random integer between the specified minimum and maximum values.

    Args:
        min_value (int, optional): The minimum value (inclusive). Defaults to 1.
        max_value (int, optional): The maximum value (inclusive). Defaults to 100.

    Returns:
        int: A random integer between min_value and max_value

    Example:
        generate_random(1, 10) -> 7
    """
    # Generate and return a random integer in the specified range
    return random.randint(min_value, max_value)

@mcp.tool()
async def long_operation(steps: int, ctx: Context) -> str:
    """Simulate a long operation with progress reporting

    This tool demonstrates how to implement a long-running asynchronous operation
    with progress reporting and logging. It simulates work by sleeping between steps.

    Args:
        steps (int): The number of steps to simulate
        ctx (Context): The MCP context object, used for progress reporting and logging

    Returns:
        str: A completion message

    Example:
        long_operation(5) -> "Completed 5 steps"
    """
    # Import asyncio here to avoid potential circular imports
    import asyncio

    # Process each step
    for i in range(steps):
        # Report progress to the client (i/steps as a fraction of completion)
        await ctx.report_progress(i, steps)

        # Log information about the current step
        # This will be visible in the client's logs
        ctx.info(f"Completed step {i+1} of {steps}")

        # Simulate work by sleeping for 0.5 seconds
        await asyncio.sleep(0.5)

    # Return a completion message
    return f"Completed {steps} steps"

# ========================================================================
# PROMPTS
# ========================================================================
# Prompts are templates for LLM interactions. They can be simple strings or
# complex multi-message conversations. Prompts are exposed to users through
# the MCP client interface, allowing them to easily invoke common interactions.

@mcp.prompt()
def simple_prompt(message: str) -> str:
    """A simple prompt that returns the message

    This prompt simply returns the provided message. It demonstrates
    the most basic form of a prompt template.

    Args:
        message (str): The message to include in the prompt

    Returns:
        str: The prompt text (identical to the input message)

    Example:
        simple_prompt("Hello, world!") -> "Hello, world!"
    """
    return message

@mcp.prompt()
def analyze_code(code: str) -> str:
    """Prompt for analyzing code

    This prompt creates a template for code analysis, asking the LLM to
    review the provided code and provide feedback on various aspects.

    Args:
        code (str): The code to be analyzed

    Returns:
        str: A formatted prompt for code analysis

    Example:
        analyze_code("print('Hello')")
    """
    # Create a formatted prompt with the code and analysis instructions
    return f"""Please analyze this code and provide feedback:

```
{code}
```

Focus on:
1. Code quality
2. Potential bugs
3. Performance issues
4. Security concerns
"""

@mcp.prompt()
def conversation_starter(topic: str) -> List[base.Message]:
    """Start a conversation about a topic

    This prompt demonstrates how to create a multi-message conversation template.
    It sets up a conversation about a specified topic with system, user, and assistant messages.

    Args:
        topic (str): The topic to discuss

    Returns:
        List[base.Message]: A list of messages forming a conversation starter

    Example:
        conversation_starter("artificial intelligence")
    """
    # Create a list of messages that form a conversation
    return [
        # System message defines the assistant's role and behavior
        base.SystemMessage("You are a helpful assistant specializing in discussing various topics."),

        # User message includes the topic parameter
        base.UserMessage(f"I'd like to discuss {topic}. What are some interesting aspects of this topic?"),

        # Assistant message provides an initial response
        base.AssistantMessage("I'd be happy to discuss this topic with you. Let me think about some interesting aspects..."),
    ]

# ========================================================================
# SERVER EXECUTION
# ========================================================================

# Run the server if this script is executed directly
if __name__ == "__main__":
    print("Starting Local Context Server...")
    print("Use Ctrl+C to stop the server")

    # The run() method starts the server and blocks until it's terminated
    # You can also use mcp.run(host="0.0.0.0", port=8000) to specify a host and port
    mcp.run()

    # Alternative ways to run the server:
    # 1. Command line: python mcp_server.py
    # 2. Development mode: mcp dev mcp_server.py
    # 3. Claude Desktop: mcp install mcp_server.py
